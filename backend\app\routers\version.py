from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
import json
import os
from typing import Dict, Any

from ..database import get_db
from ..models import User
from ..auth import get_current_admin_user
from ..routers.settings import get_settings

router = APIRouter(
    prefix="/api/v1/version",
    tags=["version"],
    responses={404: {"description": "Not found"}},
)

# 版本信息文件路径
VERSION_FILE = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "version.json")

# 默认版本信息
DEFAULT_VERSION = {
    "version": "4.3.0",
    "versionCode": 430,
    "releaseDate": "2025-01-27",
    "updateLog": "1. 优化公告页面，添加滚动条支持\n2. 适配安卓设备显示效果\n3. 暂时屏蔽暗黑模式功能，显示优化中提示\n4. 改进响应式布局，提升移动端体验\n5. 修复已知问题，提升应用稳定性",
    "downloadUrl": "https://dzwm.xyz/download/novelapp-4.3.0.apk",
    "forceUpdate": False,
    "minSupportVersion": "4.0.0"
}

def get_version_info():
    """获取版本信息"""
    if os.path.exists(VERSION_FILE):
        try:
            with open(VERSION_FILE, "r", encoding="utf-8") as f:
                return json.load(f)
        except Exception as e:
            print(f"读取版本信息文件失败: {e}")
            return DEFAULT_VERSION
    else:
        # 如果文件不存在，创建默认版本信息文件
        save_version_info(DEFAULT_VERSION)
        return DEFAULT_VERSION

def save_version_info(version_info: Dict[str, Any]):
    """保存版本信息"""
    try:
        with open(VERSION_FILE, "w", encoding="utf-8") as f:
            json.dump(version_info, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"保存版本信息文件失败: {e}")
        return False

@router.get("/")
async def read_version():
    """获取版本信息（公开接口）"""
    return get_version_info()

@router.post("/")
async def update_version(
    version_info: Dict[str, Any],
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """更新版本信息（仅管理员）"""
    # 获取当前版本信息
    current_version = get_version_info()
    
    # 更新版本信息
    for key, value in version_info.items():
        current_version[key] = value
    
    # 保存版本信息
    if save_version_info(current_version):
        return {"status": "success", "message": "版本信息已更新"}
    else:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="保存版本信息失败"
        )
